/* Mobile Responsive CSS for REIN Website */

/* New Mobile Navbar Styles */
.mobile-navbar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--white-color);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.mobile-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 10px 0;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    text-decoration: none;
    padding: 8px 5px;
    border-radius: 8px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
    min-width: 60px;
    position: relative;
}

.mobile-nav-item i {
    font-size: 20px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.mobile-nav-item span {
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mobile-nav-item.active {
    color: var(--primary-color);
    position: relative;
}

.mobile-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 5px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.mobile-nav-item.active i {
    transform: translateY(-2px);
}

.mobile-nav-item:hover {
    color: var(--secondary-color);
}

.mobile-nav-item:active {
    transform: scale(0.95);
}

.mobile-dropdown {
    position: relative;
}

.mobile-dropdown-toggle {
    background: none;
    border: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: inherit;
    width: 100%;
    cursor: pointer;
    touch-action: manipulation; /* Improve touch handling */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
    min-height: 44px; /* Minimum touch target size */
    position: relative; /* Ensure proper stacking context */
    z-index: 2; /* Ensure button is above other elements */
}

.mobile-dropdown-icon {
    font-size: 10px;
    margin-top: 2px;
    transition: all 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-icon {
    transform: rotate(180deg);
}

.mobile-dropdown-menu {
    position: fixed; /* Fixed positioning */
    bottom: 85px; /* Position with gap above the navbar */
    left: 10px; /* Inset from edges */
    right: 10px; /* Inset from edges */
    transform: none; /* Remove transform */
    width: calc(100% - 20px); /* Full width minus margins */
    background-color: var(--primary-color); /* Green background to match brand */
    border-radius: 15px; /* Rounded corners all around */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); /* Stronger shadow for depth */
    padding: 15px 0 10px; /* More padding on top */
    display: none; /* Initially hidden */
    z-index: 999; /* Just below the navbar z-index */
    margin-bottom: 0; /* No margin */
    border: none; /* No borders */
    backdrop-filter: blur(10px); /* Match navbar blur */
    -webkit-backdrop-filter: blur(10px); /* Match navbar blur */
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    pointer-events: none; /* Prevent interaction when hidden */
    touch-action: manipulation; /* Improve touch handling */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
    max-height: 300px; /* Reasonable max height */
    overflow-y: auto; /* Add scrolling if needed */
}

/* Remove the arrow indicator since the dropdown is now separate from the navbar */

.mobile-dropdown-menu a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 5px;
    color: white; /* White text on green background */
    text-decoration: none;
    font-size: 15px; /* Larger font */
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600; /* Bolder text */
    width: 20%;
    float: left;
    min-width: 60px;
    position: relative; /* For potential indicators */
    margin-bottom: 5px; /* Add some bottom margin */
}

.mobile-dropdown-menu a:last-child {
    border-bottom: none;
}

.mobile-dropdown-menu a:hover {
    color: rgba(255, 255, 255, 0.9); /* Slightly dimmed on hover */
    transform: translateY(-2px); /* Slight lift effect */
}

.mobile-dropdown-menu a:active {
    transform: scale(0.95);
}

/* Add icons to dropdown menu items */
.mobile-dropdown-menu a i {
    font-size: 24px; /* Larger icons */
    margin-bottom: 8px; /* More space below icons */
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.2); /* Semi-transparent white background */
    width: 40px; /* Fixed width */
    height: 40px; /* Fixed height */
    line-height: 40px; /* Center vertically */
    border-radius: 50%; /* Circular background */
    margin-bottom: 8px; /* Space between icon and text */
    position: relative; /* For the active indicator */
}

.mobile-dropdown-menu a:hover i {
    transform: translateY(-2px);
    background-color: rgba(255, 255, 255, 0.3); /* Lighter background on hover */
}

/* Add active indicator for the current page */
.mobile-dropdown-menu a.active i:after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.mobile-dropdown.active .mobile-dropdown-menu {
    display: block !important; /* Force display when active */
    animation: slideDown 0.3s ease;
    opacity: 1 !important; /* Force opacity when active */
    pointer-events: auto !important; /* Force enable interaction when visible */
    transform: none !important; /* No transform needed */
    visibility: visible !important; /* Ensure visibility */
    bottom: 85px !important; /* Force position above navbar with gap */
    position: fixed !important; /* Ensure fixed positioning */
    border-radius: 15px !important; /* Ensure rounded corners */
}

/* Add clearfix for floating elements */
.mobile-dropdown-menu:after {
    content: "";
    display: table;
    clear: both;
}

/* Add a title to the dropdown menu */
.mobile-dropdown-menu:before {
    content: "Our Services";
    display: block;
    text-align: center;
    color: white;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* Additional helper class for debugging */
.mobile-dropdown-visible {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    visibility: visible !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95); /* Move from above and scale */
        pointer-events: none;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1); /* Move to original position */
        pointer-events: auto;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }
}

/* Adjust body padding for mobile navbar */
@media (max-width: 767px) {
    body {
        padding-bottom: 70px; /* Add padding to body to account for fixed navbar */
    }

    /* Add subtle entrance animation for mobile navbar */
    .mobile-navbar {
        animation: slideUpFade 0.5s ease-out;
    }

    @keyframes slideUpFade {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Add subtle pulse animation to active item */
    .mobile-nav-item.active i {
        animation: subtlePulse 2s infinite;
    }

    @keyframes subtlePulse {
        0% {
            transform: translateY(-2px) scale(1);
        }
        50% {
            transform: translateY(-2px) scale(1.1);
        }
        100% {
            transform: translateY(-2px) scale(1);
        }
    }
}

/* Global Mobile Styles */
@media (max-width: 991px) {
    /* General layout adjustments */
    .container {
        padding: 0 20px;
        width: 100%;
        max-width: 100%;
    }

    section {
        padding: 60px 0;
    }

    h1 {
        font-size: 2.8rem !important;
        line-height: 1.3;
    }

    h2 {
        font-size: 2.2rem !important;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.8rem !important;
        line-height: 1.3;
    }

    h4 {
        font-size: 1.5rem !important;
        line-height: 1.3;
    }

    p {
        font-size: 18px !important;
        line-height: 1.6;
    }

    .lead {
        font-size: 20px !important;
        line-height: 1.7;
    }

    /* Header adjustments */
    .header-four {
        padding: 15px 0;
        position: sticky;
        top: 0;
        z-index: 1000;
        background-color: rgba(255, 255, 255, 0.95);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    /* Mobile menu button hidden in mobile view */
    .mobile-menu-trigger {
        display: none; /* Hide the mobile menu trigger button */
    }

    .mobile-menu-trigger svg {
        display: block;
        width: 24px;
        height: 24px;
        stroke: white;
        stroke-width: 2;
        transition: all 0.3s ease;
    }

    /* Mobile menu hidden */
    #side-bar {
        display: none; /* Hide the mobile menu sidebar completely */
    }

    #side-bar.show {
        display: none; /* Ensure it stays hidden even when 'show' class is added */
    }

    /* Overlay background hidden */
    #anywhere-home {
        display: none; /* Hide the overlay background completely */
    }

    #anywhere-home.bgshow {
        display: none; /* Ensure it stays hidden even when 'bgshow' class is added */
    }

    .close-icon-menu {
        position: absolute;
        top: 20px;
        right: 20px;
        color: #fff;
        font-size: 24px;
        cursor: pointer;
    }

    /* Mobile navigation */
    .mobile-menu-wrapper {
        padding-top: 50px;
    }

    .mobile-menu-wrapper ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .mobile-menu-wrapper ul li {
        margin-bottom: 10px;
    }

    .mobile-menu-wrapper ul li a {
        color: #fff;
        font-size: 18px;
        display: block;
        padding: 15px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        min-height: 44px; /* Minimum touch target size */
    }

    .mobile-menu-wrapper ul li a:hover {
        color: #E68C3A;
    }

    .mobile-menu-wrapper .has-dropdown > a {
        position: relative;
    }

    .mobile-menu-wrapper .has-dropdown > a::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .mobile-menu-wrapper .submenu {
        padding-left: 15px;
        display: none;
        margin-top: 5px;
        border-left: 2px solid var(--primary-color, #E68C3A);
    }

    .mobile-menu-wrapper .has-dropdown.active > a::after {
        content: '\f106';
        background-color: var(--primary-color, #E68C3A);
        transform: translateY(-50%) rotate(180deg);
    }

    .mobile-menu-wrapper .has-dropdown.active .submenu,
    .mobile-menu-wrapper .has-dropdown.mm-active .submenu {
        display: block !important;
        animation: fadeIn 0.3s ease;
    }

    /* Add click functionality for dropdown toggle */
    .mobile-menu-wrapper .has-dropdown > a::after {
        pointer-events: auto;
    }

    /* Ensure submenu items are properly styled */
    .mobile-menu-wrapper .submenu li a {
        font-size: 16px;
        padding: 12px 0;
        min-height: 40px;
    }
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
}

@media (max-width: 767px) {
    /* Banner section */
    .banner-section {
        height: auto;
        min-height: 90vh;
    }

    .banner-six-inner-content-wrapper {
        padding: 100px 0 60px;
        text-align: center;
    }

    .inner-content h1.title {
        font-size: 2.4rem !important;
        line-height: 1.3;
        margin-bottom: 20px;
    }

    .banner-description {
        font-size: 1.4rem !important;
        margin-bottom: 25px;
        line-height: 1.6;
        font-weight: 500;
    }

    /* Improve section headings */
    h2.title {
        font-size: 2rem !important;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.6rem !important;
        line-height: 1.3;
    }

    h4 {
        font-size: 1.4rem !important;
        line-height: 1.3;
    }

    /* Improve paragraph text */
    p {
        font-size: 17px !important;
        line-height: 1.6;
    }

    .lead {
        font-size: 19px !important;
        line-height: 1.7;
    }

    .banner-buttons {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .banner-buttons .btn {
        width: 100%;
        max-width: 280px;
        padding: 15px 25px !important;
        font-size: 18px !important;
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 55px;
        border-radius: 50px;
    }

    .banner-social-rotate {
        display: none;
    }

    /* Project page specific */
    .project-categories-section,
    .featured-projects-section,
    .all-projects-section {
        padding: 50px 0;
    }

    .feature-card {
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .feature-card .card-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .feature-card .card-image {
        overflow: hidden;
        position: relative;
    }

    .feature-card .card-image img {
        transition: transform 0.5s ease;
        width: 100%;
        height: auto;
    }

    .feature-card:hover .card-image img {
        transform: scale(1.05);
    }

    .service-card {
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .service-card:hover {
        transform: translateY(-5px);
    }

    .service-card .service-content {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .section-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-bottom: 30px;
    }

    .section-header .button-area {
        margin-top: 20px;
    }

    /* Contact section */
    .contact-form-wrapper,
    .contact-info-wrapper {
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .contact-form-wrapper input,
    .contact-form-wrapper textarea,
    .contact-form-wrapper select {
        padding: 15px 18px !important;
        font-size: 17px !important;
        margin-bottom: 18px;
        border-radius: 10px;
        border: 2px solid #ddd;
        line-height: 1.5;
    }

    .contact-form-wrapper .btn {
        padding: 15px 30px !important;
        font-size: 18px !important;
        font-weight: 600;
        min-height: 55px;
        border-radius: 50px;
    }

    .contact-info {
        margin-bottom: 20px;
        padding: 15px;
        border-radius: 8px;
    }

    /* Footer */
    .footer-widget {
        margin-bottom: 30px;
    }
}

@media (max-width: 575px) {
    /* Extreme small screens */
    .container {
        padding: 0 15px;
    }

    section {
        padding: 40px 0;
    }

    .inner-content h1.title {
        font-size: 2.1rem !important;
        line-height: 1.3;
    }

    .banner-tag {
        font-size: 16px !important;
        padding: 10px 18px;
        margin-bottom: 15px;
        display: inline-block;
    }

    /* Improve section headings for small screens */
    h2.title {
        font-size: 1.8rem !important;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.5rem !important;
        line-height: 1.3;
    }

    h4 {
        font-size: 1.3rem !important;
        line-height: 1.3;
    }

    /* Improve paragraph text for small screens */
    p {
        font-size: 16px !important;
        line-height: 1.6;
    }

    .lead {
        font-size: 18px !important;
        line-height: 1.7;
    }

    .banner-description {
        font-size: 1.3rem !important;
        line-height: 1.6;
        font-weight: 500;
    }

    .under-line-button,
    .ghost-button {
        width: 100%;
        text-align: center;
        padding: 12px 20px;
        font-size: 16px;
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Improve spacing for all sections */
    .section-padding {
        padding: 50px 0;
    }

    /* Improve section tags and titles */
    .section-tag {
        font-size: 16px !important;
        font-weight: 600;
        padding: 10px 20px;
        margin-bottom: 20px;
    }

    .section-description {
        font-size: 17px !important;
        line-height: 1.6;
        margin-bottom: 25px;
    }

    /* Improve card layouts */
    .row > [class*='col-'] {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Project page */
    .service-card .service-image img {
        height: 180px;
    }

    .service-content {
        padding: 25px !important;
    }

    .service-title {
        font-size: 20px !important;
        font-weight: 600;
        line-height: 1.3;
    }

    .service-category {
        font-size: 15px !important;
        font-weight: 500;
    }

    /* Improve feature cards */
    .feature-card h4 {
        font-size: 18px !important;
        font-weight: 600;
        line-height: 1.3;
    }

    .feature-card p {
        font-size: 15px !important;
        line-height: 1.5;
    }

    /* Footer */
    .footer-bottom {
        text-align: center;
        padding: 20px 0;
    }

    .footer-bottom .text-end {
        text-align: center !important;
        margin-top: 15px;
    }

    .footer-widget {
        text-align: center;
        margin-bottom: 30px;
    }

    .footer-widget ul li {
        margin-bottom: 10px;
    }

    .footer-widget ul li a {
        display: inline-block;
        padding: 10px 0;
        font-size: 17px !important;
        font-weight: 500;
        min-height: 44px;
        line-height: 1.5;
    }

    .footer-widget h4 {
        font-size: 18px !important;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .footer-widget p {
        font-size: 16px !important;
        line-height: 1.6;
    }

    /* Improve images responsiveness */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Improve table responsiveness */
    table {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Fix for iOS devices */
@media (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
    input, select, textarea, button {
        font-size: 16px !important; /* Prevents zoom on focus in iOS */
    }

    .mobile-menu-trigger {
        -webkit-tap-highlight-color: transparent;
    }
}

/* Additional mobile improvements */
@media (max-width: 767px) {
    /* Form elements improvements */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="number"],
    input[type="password"],
    select,
    textarea {
        font-size: 16px;
        padding: 12px 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        width: 100%;
        border: 1px solid #ddd;
        box-shadow: none;
        transition: border-color 0.3s, box-shadow 0.3s;
        -webkit-appearance: none;
        appearance: none;
    }

    input:focus,
    select:focus,
    textarea:focus {
        outline: none;
        border-color: var(--primary-color, #E68C3A);
        box-shadow: 0 0 0 2px rgba(230, 140, 58, 0.2);
    }

    /* Button improvements */
    .btn,
    button[type="submit"],
    input[type="submit"] {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 48px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        border: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 100%;
        margin-bottom: 10px;
    }

    /* Improve spacing between sections */
    .row {
        margin-left: -10px;
        margin-right: -10px;
    }

    /* Improve image responsiveness */
    .img-fluid {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }

    /* Improve table responsiveness */
    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}
