<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.png">
    <title>REIN - About Us</title>
    <meta name="description" content="REIN - Learn about our company, mission, vision, and the expert team behind our sustainable energy solutions.">

    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/fontawesome-6.css">
    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/swiper.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/metimenu.css">
    <link rel="stylesheet" href="css/animate.css">
    <!-- bootstrap css -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Custom css -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Custom color palette -->
    <link rel="stylesheet" href="css/custom-palette.css">
    <!-- New color scheme -->
    <link rel="stylesheet" href="css/new-color-scheme.css">
    <!-- Improved layout -->
    <link rel="stylesheet" href="css/improved-layout.css">
    <!-- Mobile responsive styles -->
    <link rel="stylesheet" href="css/mobile-responsive.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Modern Color Palette - Green & Orange Theme */
        :root {
            /* Primary Colors */
            --primary-green: #2E8B57; /* Sea Green */
            --secondary-green: #3CB371; /* Medium Sea Green */
            --light-green: rgba(46, 139, 87, 0.15); /* Transparent Sea Green */
            --dark-green: #1D6E45; /* Darker Green */

            /* Orange Accent Colors */
            --primary-orange: #FF7F50; /* Coral Orange */
            --secondary-orange: #FF6347; /* Tomato Orange */
            --light-orange: rgba(255, 127, 80, 0.15); /* Transparent Coral */
            --dark-orange: #E05E1F; /* Burnt Orange */

            /* Neutral Colors */
            --soft-cream: #F9F6F0; /* Lighter background */
            --deep-cream: #F5EEE6; /* Deeper cream for contrast */
            --dark-bg: #2C3E50; /* Dark slate for backgrounds */

            /* Functional color assignments */
            --primary-color: var(--primary-green);
            --secondary-color: var(--soft-cream);
            --accent-color: var(--primary-orange);
            --dark-accent: var(--dark-orange);
            --light-bg: var(--soft-cream);
            --text-dark: #2C3E50; /* Darker text for better readability */
            --text-light: #fff;
            --text-muted: #6c757d; /* More modern muted text */

            /* Additional colors for UI elements */
            --success-color: #4CAF50; /* Green success */
            --info-color: #2196F3; /* Blue info */
            --warning-color: #FFC107; /* Yellow warning */
            --danger-color: #F44336; /* Red danger */

            /* Legacy mappings for compatibility */
            --vibrant-orange: var(--primary-orange);
            --fern-green: var(--primary-green);
            --goldenrod: var(--secondary-orange);
            --terracotta: var(--dark-orange);
            --sienna: var(--dark-orange);
        }

        /* Modern design styles */
        body {
            font-family: 'Poppins', sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
            background-color: var(--light-bg);
            overflow-x: hidden;
            font-size: 16px;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Modern header */
        .minimal-header {
            background-color: var(--primary-green);
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 18px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .minimal-header.scrolled {
            padding: 12px 0;
            background-color: rgba(46, 139, 87, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .header-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo img {
            height: 45px;
            transition: transform 0.3s ease;
        }

        .logo:hover img {
            transform: scale(1.05);
        }

        .main-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .main-nav li {
            margin: 0 20px;
            position: relative;
        }

        .main-nav a {
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            padding: 8px 5px;
            font-size: 1.05rem;
            letter-spacing: 0.5px;
            margin: 0 5px;
            border-radius: 4px;
        }

        .main-nav a:hover {
            color: var(--text-light);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .main-nav a::after {
            content: '';
            position: absolute;
            width: 40%;
            height: 3px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            background-color: var(--primary-orange);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-radius: 3px;
        }

        .main-nav a:hover::after {
            transform: translateX(-50%) scaleX(1);
        }

        .main-nav a.active {
            color: var(--text-light);
            font-weight: 600;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .main-nav a.active::after {
            transform: translateX(-50%) scaleX(1);
            background-color: var(--primary-orange);
            width: 60%;
        }

        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .mobile-menu-toggle span {
            width: 25px;
            height: 2px;
            background-color: var(--text-light);
            margin: 3px 0;
            transition: all 0.3s;
        }

        /* Mobile menu button improvements */
        .mobile-menu-trigger {
            padding: 10px;
            cursor: pointer;
            z-index: 100;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 44px;
            min-height: 44px;
        }

        .mobile-menu-trigger:hover {
            background-color: rgba(0, 0, 0, 0.4);
        }

        .mobile-menu-trigger svg {
            display: block;
        }

        /* Modern Banner section */
        .banner-section {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 160px 0 60px; /* Increased top padding to account for fixed header */
            position: relative;
            overflow: hidden;
        }

        .banner-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(135deg,
                rgba(46, 139, 87, 0.85) 0%,
                rgba(60, 179, 113, 0.75) 50%,
                rgba(29, 110, 69, 0.8) 100%),
                url('imagesprj/13.jpg');
            background-size: cover;
            background-position: center;
            z-index: 0;
            animation: gradientShift 15s ease infinite alternate;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .banner-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .banner-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 30px;
            letter-spacing: 1px;
            color: var(--text-light);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .mission-vision-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .mission-box, .vision-box {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 40px;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            border-top: 5px solid var(--primary-green);
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .mission-box::before, .vision-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(46, 139, 87, 0.05) 0%,
                rgba(255, 127, 80, 0.05) 100%);
            z-index: 0;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .mission-box:hover, .vision-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-top: 5px solid var(--primary-orange);
        }

        .mission-box:hover::before, .vision-box:hover::before {
            opacity: 1;
        }

        .vision-box {
            border-top: 5px solid var(--primary-orange);
        }

        .vision-box:hover {
            border-top: 5px solid var(--primary-green);
        }

        .box-title {
            font-size: 1.8rem;
            margin-bottom: 22px;
            display: flex;
            align-items: center;
            color: var(--primary-green);
            position: relative;
            z-index: 1;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .mission-box:hover .box-title {
            color: var(--primary-orange);
        }

        .vision-box .box-title {
            color: var(--primary-orange);
        }

        .vision-box:hover .box-title {
            color: var(--primary-green);
        }

        .box-title i {
            margin-right: 15px;
            color: var(--primary-orange);
            font-size: 2rem;
            transition: all 0.3s ease;
        }

        .mission-box:hover .box-title i {
            color: var(--primary-green);
            transform: scale(1.1) rotate(5deg);
        }

        .vision-box .box-title i {
            color: var(--primary-green);
        }

        .vision-box:hover .box-title i {
            color: var(--primary-orange);
            transform: scale(1.1) rotate(5deg);
        }

        /* Content sections */
        .content-section {
            padding: 60px 0;
            position: relative;
        }

        .content-section.alt {
            background-color: var(--deep-cream);
            position: relative;
            overflow: hidden;
        }

        .content-section.alt::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle,
                rgba(46, 139, 87, 0.1) 0%,
                rgba(46, 139, 87, 0) 70%);
            border-radius: 50%;
            z-index: 0;
            animation: pulse 15s infinite alternate;
        }

        .content-section.alt::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle,
                rgba(255, 127, 80, 0.1) 0%,
                rgba(255, 127, 80, 0) 70%);
            border-radius: 50%;
            z-index: 0;
            animation: pulse 15s infinite alternate-reverse;
        }

        @keyframes pulse {
            0% {
                opacity: 0.5;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
            100% {
                opacity: 0.5;
                transform: scale(1);
            }
        }

        .section-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 30px;
            position: relative;
            display: inline-block;
            color: var(--primary-green);
            letter-spacing: -0.5px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section-title::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 5px;
            background: linear-gradient(to right, var(--primary-green), var(--primary-orange));
            bottom: -15px;
            left: 0;
            border-radius: 5px;
        }

        .section-subtitle {
            color: var(--text-muted);
            margin-bottom: 60px;
            max-width: 750px;
            font-size: 1.15rem;
            line-height: 1.8;
        }

        /* Feature cards */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin-top: 70px;
            position: relative;
            z-index: 1;
        }

        .feature-card {
            background-color: #fff;
            border-radius: 20px;
            padding: 45px 35px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.05);
            transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
            border-top: 6px solid var(--primary-green);
            position: relative;
            overflow: hidden;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 0;
            background: linear-gradient(180deg,
                rgba(46, 139, 87, 0.08) 0%,
                rgba(255, 255, 255, 0) 100%);
            transition: height 0.5s ease;
            z-index: 0;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 0 25px 50px rgba(0,0,0,0.1);
            border-top: 6px solid var(--primary-orange);
        }

        .feature-card:hover::after {
            height: 100%;
        }

        .feature-card:nth-child(even) {
            border-top: 6px solid var(--primary-orange);
        }

        .feature-card:nth-child(even):hover {
            border-top: 6px solid var(--primary-green);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 30px;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            z-index: 1;
            filter: drop-shadow(0 5px 15px rgba(0,0,0,0.1));
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.15) rotate(5deg);
        }

        .feature-card:nth-child(even):hover .feature-icon {
            transform: scale(1.15) rotate(-5deg);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-green);
            position: relative;
            z-index: 1;
            transition: color 0.4s ease;
        }

        .feature-card:nth-child(even) .feature-title {
            color: var(--primary-orange);
        }

        .feature-card:hover .feature-title {
            color: var(--primary-orange);
        }

        .feature-card:nth-child(even):hover .feature-title {
            color: var(--primary-green);
        }

        .feature-card p {
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
            line-height: 1.8;
            font-size: 1.05rem;
            color: var(--text-muted);
        }

        .feature-card:hover p {
            color: var(--text-dark);
        }

        /* Goals section */
        .goals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .goal-card {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .goal-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .goal-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .goal-image::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30%;
            background: linear-gradient(to top, rgba(46, 139, 87, 0.7), transparent);
        }

        .goal-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .goal-card:hover .goal-image img {
            transform: scale(1.05);
        }

        .goal-content {
            padding: 25px;
        }

        .goal-tag {
            color: var(--primary-orange);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 10px;
            display: block;
        }

        .goal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-green);
        }

        /* Solar Solutions Section */
        .solar-solutions-wrapper {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .solar-solutions-image {
            flex: 1;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            transform: perspective(1000px) rotateY(0deg);
            transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .solar-solutions-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(46, 139, 87, 0.2) 0%,
                rgba(255, 127, 80, 0.2) 100%);
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .solar-solutions-image img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
            filter: saturate(1.1);
        }

        .solar-solutions-image:hover {
            transform: perspective(1000px) rotateY(-5deg);
            box-shadow: 30px 20px 40px rgba(0, 0, 0, 0.15);
        }

        .solar-solutions-image:hover::after {
            opacity: 1;
        }

        .solar-solutions-image:hover img {
            transform: scale(1.08);
        }

        .solar-solutions-content {
            flex: 1;
            padding: 20px;
        }

        .solutions-label {
            display: inline-block;
            background-color: rgba(46, 139, 87, 0.1);
            color: var(--primary-green);
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            letter-spacing: 1px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .solutions-label::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(46, 139, 87, 0) 0%,
                rgba(46, 139, 87, 0.1) 50%,
                rgba(46, 139, 87, 0) 100%);
            animation: shimmer 2s infinite;
            z-index: 1;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .solutions-label span {
            color: var(--primary-orange);
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .solutions-label span::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-orange);
        }

        .solutions-tabs {
            margin: 30px 0;
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 10px 20px;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn:hover {
            color: var(--vibrant-orange);
        }

        .tab-btn.active {
            color: var(--dark-orange);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -11px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--vibrant-orange), var(--dark-orange));
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .benefits-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .benefits-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .benefit-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            margin-right: 15px;
            color: var(--vibrant-orange);
            flex-shrink: 0;
        }

        .benefit-icon i.fa-circle-check {
            color: var(--vibrant-orange);
        }

        .benefit-icon i.fa-bolt {
            color: var(--light-orange);
        }

        .benefit-icon i.fa-lightbulb {
            color: var(--dark-orange);
        }

        .benefit-text {
            flex: 1;
        }

        .benefit-text strong {
            display: block;
            margin-bottom: 5px;
            color: var(--dark-orange);
        }

        .expertise-points {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 30px;
        }

        .expertise-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .expertise-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: rgba(255, 122, 69, 0.1);
            color: var(--vibrant-orange);
            border-radius: 50%;
        }

        .expertise-text {
            font-weight: 500;
            color: #333;
        }

        /* Services section */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .service-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            border-bottom: 4px solid var(--primary-green);
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-bottom: 4px solid var(--primary-orange);
        }

        .service-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 20px;
            filter: hue-rotate(45deg);
        }

        .service-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-green);
        }

        /* Modern Footer */
        .minimal-footer {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 60px 0 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 -10px 30px rgba(0,0,0,0.1);
        }

        .minimal-footer::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle,
                rgba(46, 139, 87, 0.15) 0%,
                rgba(44, 62, 80, 0) 70%);
            border-radius: 50%;
            z-index: 0;
            animation: pulse 15s infinite alternate;
        }

        .minimal-footer::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle,
                rgba(255, 127, 80, 0.1) 0%,
                rgba(44, 62, 80, 0) 70%);
            border-radius: 50%;
            z-index: 0;
            animation: pulse 15s infinite alternate-reverse;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 50px;
            margin-bottom: 60px;
            position: relative;
            z-index: 1;
        }

        .footer-logo img {
            height: 50px;
            margin-bottom: 25px;
            transition: transform 0.3s ease;
        }

        .footer-logo:hover img {
            transform: scale(1.05);
        }

        .footer-description {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 25px;
            line-height: 1.7;
            font-size: 0.95rem;
        }

        .footer-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 25px;
            position: relative;
            padding-bottom: 12px;
            color: var(--vibrant-orange);
            letter-spacing: 0.5px;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--vibrant-orange), var(--dark-orange));
            bottom: 0;
            left: 0;
            border-radius: 2px;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 15px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            padding-left: 18px;
            display: inline-block;
            font-size: 1.05rem;
        }

        .footer-links a::before {
            content: '→';
            position: absolute;
            left: 0;
            top: 0;
            color: var(--primary-green);
            opacity: 0;
            transform: translateX(-5px);
            transition: all 0.4s ease;
        }

        .footer-links a:hover {
            color: var(--primary-orange);
            padding-left: 25px;
        }

        .footer-links a:hover::before {
            opacity: 1;
            transform: translateX(0);
            color: var(--primary-orange);
        }

        .social-links {
            display: flex;
            gap: 18px;
            margin-top: 30px;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-color: rgba(46, 139, 87, 0.2);
            color: #fff;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .social-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-green), var(--primary-orange));
            opacity: 0;
            transition: opacity 0.4s ease;
            z-index: 0;
        }

        .social-links a i {
            position: relative;
            z-index: 1;
            transition: all 0.4s ease;
            font-size: 1.2rem;
        }

        .social-links a:hover {
            transform: translateY(-8px) scale(1.15);
            box-shadow: 0 15px 25px rgba(0,0,0,0.2);
        }

        .social-links a:hover::before {
            opacity: 1;
        }

        .social-links a:hover i {
            transform: scale(1.2) rotate(10deg);
            color: #fff;
        }

        .social-links a:nth-child(even):hover i {
            transform: scale(1.2) rotate(-10deg);
        }

        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
            position: relative;
            z-index: 1;
        }

        .copyright a {
            color: var(--vibrant-orange);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .copyright a:hover {
            color: var(--dark-orange);
        }

        /* Modern Buttons */
        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: #fff;
            padding: 16px 38px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            z-index: 1;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
            box-shadow: 0 10px 25px rgba(46, 139, 87, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-orange), var(--secondary-orange));
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: -1;
        }

        .btn-primary:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 20px 40px rgba(46, 139, 87, 0.4);
        }

        .btn-primary:hover::before {
            opacity: 1;
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 100%;
            top: 0;
            left: -100px;
            background: rgba(255, 255, 255, 0.2);
            transform: skewX(-25deg);
            transition: all 0.8s ease;
        }

        .btn-primary:hover::after {
            left: 200%;
        }

        .btn-outline {
            display: inline-block;
            background: transparent;
            color: #fff;
            padding: 15px 36px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
            border: 2px solid var(--primary-orange);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            z-index: 1;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .btn-outline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.5s cubic-bezier(0.86, 0, 0.07, 1);
            z-index: -1;
        }

        .btn-outline:hover {
            color: #fff;
            border-color: transparent;
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .btn-outline:hover::before {
            transform: scaleX(1);
            transform-origin: left;
        }

        .btn-outline::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 100%;
            top: 0;
            left: -100px;
            background: rgba(255, 255, 255, 0.2);
            transform: skewX(-25deg);
            transition: all 0.8s ease;
            z-index: -1;
        }

        .btn-outline:hover::after {
            left: 200%;
        }

        /* Modern Team section */
        .section-header {
            text-align: center;
            margin-bottom: 70px;
            position: relative;
        }

        .team-label {
            display: inline-block;
            background-color: rgba(46, 139, 87, 0.1);
            color: var(--primary-green);
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 25px;
            letter-spacing: 1px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .team-label::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(46, 139, 87, 0.05);
            border-radius: 50px;
            transform: scale(1.2);
            z-index: -1;
            opacity: 0.5;
            animation: pulse 3s infinite alternate;
        }

        .team-label span {
            color: var(--primary-orange);
            font-weight: 700;
            position: relative;
        }

        .team-label span::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-orange);
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 50px;
            position: relative;
            z-index: 1;
        }

        .team-member {
            background-color: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.12);
            transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
            position: relative;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .team-member::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
            opacity: 0;
            transition: opacity 0.5s ease;
            z-index: -1;
            border-radius: 20px;
        }

        .team-member:hover {
            transform: translateY(-20px) scale(1.03);
        }

        .team-member:hover::after {
            opacity: 1;
        }

        .member-image {
            position: relative;
            height: 400px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .member-image::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(to top,
                rgba(46, 139, 87, 0.8) 0%,
                rgba(46, 139, 87, 0) 100%);
            z-index: 1;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .team-member:hover .member-image::before {
            opacity: 1;
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
            filter: saturate(1.1);
            object-position: center;
        }

        .team-member:hover .member-image img {
            transform: scale(1.1);
        }

        .share-button, .linkedin-button {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 45px;
            height: 45px;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
            opacity: 0;
            transform: translateY(20px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            text-decoration: none;
        }

        .share-button {
            background-color: var(--vibrant-orange);
        }

        .linkedin-button {
            background-color: #0077B5; /* LinkedIn blue color */
        }

        .team-member:hover .share-button,
        .team-member:hover .linkedin-button {
            opacity: 1;
            transform: translateY(0);
        }

        .share-button:hover {
            background-color: var(--dark-orange);
            transform: scale(1.15) rotate(10deg);
        }

        .linkedin-button:hover {
            background-color: #005e93; /* Darker LinkedIn blue */
            transform: scale(1.15) rotate(10deg);
        }

        .share-button .tooltip {
            position: absolute;
            bottom: 55px;
            right: -15px;
            background-color: var(--dark-bg);
            color: #fff;
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 0.85rem;
            white-space: nowrap;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 10;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .share-button:hover .tooltip {
            opacity: 1;
            transform: translateY(0);
        }

        .share-button .tooltip::after {
            content: '';
            position: absolute;
            bottom: -6px;
            right: 22px;
            width: 12px;
            height: 12px;
            background-color: var(--dark-bg);
            transform: rotate(45deg);
        }

        .member-info {
            padding: 25px;
            text-align: center;
            border-top: 5px solid var(--vibrant-orange);
            position: relative;
            transition: all 0.3s ease;
            background-color: #ffffff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .member-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 5px;
            background: linear-gradient(to right, var(--vibrant-orange), var(--dark-orange));
            transition: width 0.4s ease;
            z-index: 1;
        }

        .team-member:hover .member-info::before {
            width: 100%;
        }

        .member-name {
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 12px;
            color: #333;
            transition: color 0.3s ease;
            text-shadow: 0px 0px 1px rgba(0,0,0,0.1);
        }

        .team-member:hover .member-name {
            color: #ff6b35;
        }

        .member-position {
            color: #333333;
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 0.5px;
            position: relative;
            display: inline-block;
            padding-bottom: 5px;
            text-shadow: 0px 0px 1px rgba(0,0,0,0.1);
        }

        .member-position::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background-color: var(--vibrant-orange);
            transition: width 0.3s ease, background-color 0.3s ease;
        }

        .team-member:hover .member-position::after {
            width: 50px;
            background-color: var(--dark-orange);
        }

        /* Contact Cards Styling */
        .contact-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .contact-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .contact-card:hover {
            transform: translateY(-10px);
        }

        .contact-card:hover .contact-icon {
            transform: scale(1.1) rotate(10deg);
            transition: all 0.3s ease;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .mission-vision-grid {
                grid-template-columns: 1fr;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .main-nav {
                display: none;
            }

            /* Mobile menu button improvements */
            .mobile-menu-trigger {
                padding: 12px;
                cursor: pointer;
                z-index: 100;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 8px;
                transition: all 0.3s ease;
                min-width: 48px;
                min-height: 48px;
                touch-action: manipulation;
            }

            .mobile-menu-trigger:hover {
                background-color: rgba(0, 0, 0, 0.4);
            }

            .mobile-menu-trigger:active {
                background-color: rgba(0, 0, 0, 0.6);
                transform: scale(0.95);
            }

            .mobile-menu-trigger svg {
                display: block;
                width: 24px;
                height: 24px;
            }

            .main-nav.active {
                display: block;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background-color: #fff;
                box-shadow: 0 5px 10px rgba(0,0,0,0.1);
                padding: 20px;
            }

            .main-nav.active ul {
                flex-direction: column;
            }

            .main-nav.active li {
                margin: 10px 0;
            }

            .team-grid {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            }

            .solar-solutions-wrapper {
                flex-direction: column;
            }

            .solar-solutions-image, .solar-solutions-content {
                width: 100%;
            }

            .section-title {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 768px) {
            .banner-section {
                padding: 140px 0 60px !important; /* Adjusted for mobile header */
            }

            .expertise-points {
                grid-template-columns: 1fr;
            }

            .tab-buttons {
                flex-wrap: wrap;
            }

            .tab-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .team-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .solutions-label {
                font-size: 0.8rem;
            }

            .benefit-text strong {
                font-size: 0.95rem;
            }

            .benefit-text {
                font-size: 0.9rem;
            }

            .contact-cards {
                flex-direction: column;
            }

            .contact-card {
                width: 100%;
                margin-bottom: 20px;
            }
        }

        /* Contact Cards Section */
        .contact-section {
            padding: 50px 0;
            background-color: #f9f8f4;
            position: relative;
            overflow: hidden;
        }

        .contact-section .section-header {
            margin-bottom: 40px;
        }

        .contact-section .team-label {
            display: inline-block;
            background-color: rgba(46, 139, 87, 0.1);
            color: #333;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 30px;
            margin-bottom: 15px;
        }

        .contact-section .team-label span {
            color: var(--primary-green);
            border-bottom: 2px solid var(--primary-green);
            padding-bottom: 2px;
        }

        .contact-section .section-title {
            position: relative;
            margin-bottom: 30px;
        }

        .contact-section .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: var(--primary-green);
            margin: 15px auto 0;
        }

        .contact-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 30px;
            justify-content: center;
        }

        .contact-card {
            flex: 1;
            min-width: 300px;
            max-width: 500px;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .phone-card {
            background-color: #2d2d2d;
        }

        .email-card {
            background-color: var(--primary-green);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1;
        }

        .phone-card .contact-icon {
            background-color: rgba(46, 139, 87, 0.2);
        }

        .email-card .contact-icon {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .contact-icon i {
            font-size: 30px;
            color: white;
        }

        .contact-info {
            background-color: white;
            padding: 25px 25px 25px 130px;
            margin: 30px;
            border-radius: 10px;
            position: relative;
            z-index: 0;
        }

        .contact-info h3 {
            color: #ff6b35;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            flex-direction: column;
        }

        .contact-info h3 span {
            display: block;
        }

        .contact-info p {
            color: #333;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .contact-info a {
            display: block;
            color: var(--primary-green);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
            font-size: 18px;
        }

        .contact-info a:hover {
            color: #ff6b35;
        }

        /* Enhanced "Meet Our Team" button styling */
        .under-line-button {
            display: inline-block !important;
            background: linear-gradient(135deg, #E68C3A 0%, #FF7A00 100%) !important;
            color: #fff !important;
            padding: 18px 40px !important;
            border-radius: 50px !important;
            font-weight: 700 !important;
            font-size: 16px !important;
            text-decoration: none !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            position: relative !important;
            overflow: hidden !important;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
            box-shadow: 0 8px 25px rgba(230, 140, 58, 0.4) !important;
            border: none !important;
            z-index: 1 !important;
        }

        .under-line-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #336021 0%, #2E5A1A 100%);
            transition: all 0.4s ease;
            z-index: -1;
        }

        .under-line-button:hover {
            color: #fff !important;
            transform: translateY(-8px) scale(1.05) !important;
            box-shadow: 0 15px 35px rgba(51, 96, 33, 0.5) !important;
        }

        .under-line-button:hover::before {
            left: 0;
        }

        .under-line-button:active {
            transform: translateY(-4px) scale(1.02) !important;
        }

        /* Enhanced Contact Info Boxes */
        .contact-info-wrapper {
            display: flex !important;
            flex-direction: column !important;
            gap: 25px !important;
            height: 100% !important;
        }

        .contact-info {
            display: flex !important;
            align-items: center !important;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%) !important;
            border-radius: 20px !important;
            padding: 30px 25px !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid rgba(230, 140, 58, 0.1) !important;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
            position: relative !important;
            overflow: hidden !important;
            margin-bottom: 0 !important;
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(230, 140, 58, 0.05) 0%, rgba(51, 96, 33, 0.05) 100%);
            transition: all 0.4s ease;
            z-index: 0;
        }

        .contact-info:hover {
            transform: translateY(-10px) scale(1.02) !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
            border-color: rgba(230, 140, 58, 0.3) !important;
        }

        .contact-info:hover::before {
            left: 0;
        }

        .contact-info .icon {
            width: 70px !important;
            height: 70px !important;
            background: linear-gradient(135deg, #E68C3A 0%, #FF7A00 100%) !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin-right: 25px !important;
            box-shadow: 0 8px 20px rgba(230, 140, 58, 0.3) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .contact-info:hover .icon {
            background: linear-gradient(135deg, #336021 0%, #2E5A1A 100%) !important;
            box-shadow: 0 12px 25px rgba(51, 96, 33, 0.4) !important;
            transform: scale(1.1) rotate(5deg) !important;
        }

        .contact-info .icon i {
            font-size: 24px !important;
            color: #fff !important;
        }

        .contact-info .content {
            flex: 1 !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .contact-info .content h5 {
            font-size: 20px !important;
            font-weight: 700 !important;
            color: #2C3E50 !important;
            margin-bottom: 8px !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        .contact-info .content p {
            margin: 0 !important;
            font-size: 16px !important;
            color: #6c757d !important;
            line-height: 1.6 !important;
        }

        .contact-info .content a {
            color: #336021 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
        }

        .contact-info .content a:hover {
            color: #E68C3A !important;
            text-decoration: underline !important;
        }

        /* Special styling for email container */
        .email-container {
            margin-bottom: 8px !important;
        }

        .email-container:last-child {
            margin-bottom: 0 !important;
        }

        /* Address line styling */
        .address-line {
            margin-bottom: 5px !important;
        }

        .address-line:last-child {
            margin-bottom: 0 !important;
        }

        /* Contact map styling */
        .contact-map {
            margin-top: 30px !important;
            border-radius: 20px !important;
            overflow: hidden !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            border: 3px solid rgba(230, 140, 58, 0.2) !important;
            transition: all 0.3s ease !important;
        }

        .contact-map:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
            border-color: rgba(230, 140, 58, 0.4) !important;
            transform: translateY(-5px) !important;
        }

        .contact-map iframe {
            border-radius: 17px !important;
            transition: all 0.3s ease !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .under-line-button {
                padding: 15px 30px !important;
                font-size: 14px !important;
            }

            .contact-info {
                padding: 25px 20px !important;
            }

            .contact-info .icon {
                width: 60px !important;
                height: 60px !important;
                margin-right: 20px !important;
            }

            .contact-info .icon i {
                font-size: 20px !important;
            }

            .contact-info .content h5 {
                font-size: 18px !important;
            }

            .contact-info .content p {
                font-size: 14px !important;
            }
        }

        /* Enhanced Services Dropdown Menu Styling */
        .main-nav-one ul li.has-dropdown .submenu {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%) !important;
            border-radius: 15px !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
            border: 1px solid rgba(230, 140, 58, 0.1) !important;
            padding: 15px 0 !important;
            min-width: 280px !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transform: translateY(-20px) !important;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
            z-index: 9999 !important;
        }

        .main-nav-one ul li.has-dropdown:hover .submenu {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li {
            margin: 0 !important;
            padding: 0 !important;
            border-bottom: none !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a {
            display: block !important;
            padding: 15px 25px !important;
            color: #2C3E50 !important;
            font-weight: 600 !important;
            font-size: 15px !important;
            text-decoration: none !important;
            border-bottom: 1px solid rgba(230, 140, 58, 0.1) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li:last-child a {
            border-bottom: none !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(230, 140, 58, 0.1) 0%, rgba(51, 96, 33, 0.1) 100%);
            transition: all 0.3s ease;
            z-index: 0;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover {
            color: #E68C3A !important;
            padding-left: 35px !important;
            background-color: rgba(230, 140, 58, 0.05) !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover::before {
            left: 0;
        }

        /* Add icons to dropdown items */
        .main-nav-one ul li.has-dropdown .submenu li a::after {
            content: '\f105';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #E68C3A;
            font-size: 12px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover::after {
            opacity: 1;
            right: 15px;
        }

        /* Enhanced dropdown arrow */
        .main-nav-one ul li.has-dropdown a::before {
            content: "\f078" !important;
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            color: #fff !important;
            font-size: 12px !important;
            transition: all 0.3s ease !important;
        }

        .main-nav-one ul li.has-dropdown:hover a::before {
            content: "\f077" !important;
            color: #E68C3A !important;
        }

        /* Special styling for active dropdown item */
        .main-nav-one ul li.has-dropdown .submenu li a.active {
            background: linear-gradient(135deg, #E68C3A 0%, #FF7A00 100%) !important;
            color: #fff !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a.active::after {
            color: #fff !important;
            opacity: 1 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .main-nav-one ul li.has-dropdown .submenu {
                min-width: 250px !important;
            }
        }

        @media (max-width: 991px) {
            .main-nav-one ul li.has-dropdown .submenu {
                position: static !important;
                opacity: 1 !important;
                visibility: visible !important;
                transform: none !important;
                box-shadow: none !important;
                border: none !important;
                border-radius: 0 !important;
                background: transparent !important;
                padding: 0 !important;
                margin-left: 20px !important;
            }

            .main-nav-one ul li.has-dropdown .submenu li a {
                padding: 10px 15px !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
                color: #fff !important;
            }

            .main-nav-one ul li.has-dropdown .submenu li a:hover {
                background-color: rgba(255, 255, 255, 0.1) !important;
                color: #E68C3A !important;
                padding-left: 25px !important;
            }
        }
    </style>
</head>

<body style="visibility: visible !important; opacity: 1 !important;">
    <noscript>
        <style>
            #elevate-load { display: none !important; }
            body { overflow: auto !important; visibility: visible !important; }
        </style>
        <div style="padding: 20px; background-color: #f8d7da; color: #721c24; text-align: center; margin: 20px;">
            <p>This website works best with JavaScript enabled. Please enable JavaScript for the best experience.</p>
        </div>
    </noscript>
    <!-- Header area start -->
    <header class="header-four header--sticky">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="header-left">
                        <a href="index.html" class="logo-area">
                            <img src="images/logo-02.png" alt="logo">
                        </a>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 d-none d-md-block">
                    <div class="nav-area">
                        <!-- navigation area start -->
                        <div class="header-nav main-nav-one">
                            <nav>
                                <ul>
                                    <li>
                                        <a class="nav-link" href="index.html">Home</a>
                                    </li>
                                    <li>
                                        <a class="nav-link active" href="aboutus.html">About</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="project.html">Project</a>
                                    </li>
                                    <li class="has-dropdown">
                                        <a class="nav-link" href="service.html">Services</a>
                                        <ul class="submenu">
                                            <li><a href="#">Design & Consultancy</a></li>
                                            <li><a href="#">Solar, Battery & EV</a></li>
                                            <li><a href="#">Wind</a></li>
                                            <li><a href="#">Energy Audit, Monitoring & Management</a></li>
                                            <li><a href="#">Servicing</a></li>
                                        </ul>
                                    </li>
                                    <li><a class="nav-link" href="contact.html">Contact</a></li>
                                </ul>
                            </nav>
                        </div>
                        <!-- navigation area end -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-6">
                    <div class="header-right">
                        <div class="action-button-menu">
                            <a href="contact.html" class="contact">Let's Talk</a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header area end -->

    <!-- New Mobile Navigation Bar -->
    <nav class="mobile-navbar d-md-none">
        <div class="mobile-nav-container">
            <a href="index.html" class="mobile-nav-item">
                <i class="fa-solid fa-home"></i>
                <span>Home</span>
            </a>
            <a href="aboutus.html" class="mobile-nav-item active">
                <i class="fa-solid fa-info-circle"></i>
                <span>About</span>
            </a>
            <a href="project.html" class="mobile-nav-item">
                <i class="fa-solid fa-project-diagram"></i>
                <span>Projects</span>
            </a>
            <a href="service.html" class="mobile-nav-item">
                <i class="fa-solid fa-solar-panel"></i>
                <span>Services</span>
            </a>
            <a href="contact.html" class="mobile-nav-item">
                <i class="fa-solid fa-envelope"></i>
                <span>Contact</span>
            </a>
        </div>
    </nav>

    <!-- Legacy elements kept for compatibility -->
    <div id="side-bar" class="side-bar d-none"></div>
    <div id="anywhere-home" class="d-none"></div>

    <!-- Banner area start -->
    <section class="banner-section">
        <div class="rts-banner-six-area bg-image-solution banner-six-height">
            <video muted loop autoplay playsinline poster="imagesprj/13.jpg">
                <source src="media/18.webm" type="video/mp4">
                <!-- Fallback image if video fails to load -->
                <img src="imagesprj/13.jpg" alt="Banner Background">
            </video>
            <!-- Completely transparent overlay -->
            <div class="banner-overlay"></div>

            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="banner-six-inner-content-wrapper">
                            <div class="inner-content">
                                <span class="banner-tag">About Our Company</span>
                                <h1 class="title">
                                    Powering a <span class="highlight-light">Sustainable</span> <br>
                                    Future with <span class="highlight-orange">Expert Team</span>
                                </h1>
                                <p class="banner-description">Learn about our mission, vision, and the dedicated team of professionals working to revolutionize the renewable energy landscape with innovative solutions.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="banner-social-rotate">
                <!-- social wrapper start-->
                <div class="social-wrapper-one-horizental">
                    <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                    <a href="#"><i class="fa-brands fa-youtube"></i></a>
                    <a href="#"><i class="fa-brands fa-twitter"></i></a>
                    <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                </div>
                <!-- social wrapper end -->
                <p class="follow">Follow us</p>
            </div>

            <!-- Scroll indicator -->
            <div class="scroll-indicator">
                <p>SCROLL DOWN</p>
                <div class="scroll-mouse">
                    <div class="scroll-dot"></div>
                </div>
            </div>
        </div>
    </section>
    <!-- Banner area end -->

    <!-- Solar Solutions section -->
    <section id="solar-solutions" class="content-section">
        <div class="container">
            <div class="solar-solutions-wrapper">
                <div class="solar-solutions-image">
                    <img src="images/3d.png" alt="Solar Energy Solutions">
                </div>
                <div class="solar-solutions-content">
                    <div class="solutions-label">ABOUT <span>COMPANY</span></div>
                    <h2 class="section-title">Revolutionizing Solar Energy Solutions</h2>

                    <div class="solutions-tabs">
                        <div class="tab-buttons">
                            <button class="tab-btn active" data-tab="why-us">Why Choose Us?</button>
                            <button class="tab-btn" data-tab="mission">Our Mission</button>
                            <button class="tab-btn" data-tab="goal">Our Goal</button>
                        </div>

                        <div class="tab-content active" id="why-us-content">
                            <ul class="benefits-list">
                                <li>
                                    <span class="benefit-icon"><i class="fa-solid fa-circle-check"></i></span>
                                    <div class="benefit-text">
                                        <strong>Mitigate Carbon Emissions:</strong> We've already helped reduce 146 million tons of carbon emissions globally.
                                    </div>
                                </li>
                                <li>
                                    <span class="benefit-icon"><i class="fa-solid fa-bolt"></i></span>
                                    <div class="benefit-text">
                                        <strong>Empower Energy Independence:</strong> From residential solar to utility-scale projects, we provide solutions that empower energy independence.
                                    </div>
                                </li>
                                <li>
                                    <span class="benefit-icon"><i class="fa-solid fa-lightbulb"></i></span>
                                    <div class="benefit-text">
                                        <strong>Innovate for the Future:</strong> We're constantly exploring new ways to optimize solar designs and integrate emerging technologies like battery storage and EV charging.
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="tab-content" id="mission-content">
                            <p>Our mission is to accelerate the world's transition to sustainable energy through innovative solar solutions. We believe in a future where clean, renewable energy is accessible to everyone, reducing our collective carbon footprint and creating a more sustainable planet for future generations.</p>
                        </div>

                        <div class="tab-content" id="goal-content">
                            <p>Our goal is to become the leading provider of integrated solar energy solutions worldwide, known for excellence in design, implementation, and customer service. We aim to continuously push the boundaries of what's possible in renewable energy technology.</p>
                        </div>
                    </div>

                    <div class="expertise-points">
                        <div class="expertise-item">
                            <span class="expertise-icon"><i class="fa-solid fa-users"></i></span>
                            <span class="expertise-text">Experienced Team of Experts</span>
                        </div>
                        <div class="expertise-item">
                            <span class="expertise-icon"><i class="fa-solid fa-headset"></i></span>
                            <span class="expertise-text">Very First Customers Service</span>
                        </div>
                        <div class="expertise-item">
                            <span class="expertise-icon"><i class="fa-solid fa-arrows-to-circle"></i></span>
                            <span class="expertise-text">Flexibility Work Environment</span>
                        </div>
                        <div class="expertise-item">
                            <span class="expertise-icon"><i class="fa-solid fa-medal"></i></span>
                            <span class="expertise-text">Provide Quality Service</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us section -->
    <section id="why-choose-us" class="content-section">
        <div class="container">
            <h2 class="section-title">Why Choose Us?</h2>
            <p class="section-subtitle">We stand out in the renewable energy industry with our commitment to excellence, innovation, and customer satisfaction.</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <img src="imagesprj/01_1.png" alt="Experienced Team" class="feature-icon">
                    <h3 class="feature-title">Experienced Team</h3>
                    <p>Our experts in solar energy and renewable technologies bring years of industry experience to every project we undertake.</p>
                </div>

                <div class="feature-card">
                    <img src="imagesprj/02_1.png" alt="Customer Service" class="feature-icon">
                    <h3 class="feature-title">Customer Service</h3>
                    <p>We prioritize client needs with 24/7 support and personalized attention throughout your renewable energy journey.</p>
                </div>

                <div class="feature-card">
                    <img src="imagesprj/05.png" alt="Innovative Solutions" class="feature-icon">
                    <h3 class="feature-title">Innovative Solutions</h3>
                    <p>We develop cutting-edge renewable energy solutions that are both efficient and environmentally responsible.</p>
                </div>

                <div class="feature-card">
                    <img src="imagesprj/04.png" alt="Quality Service" class="feature-icon">
                    <h3 class="feature-title">Quality Service</h3>
                    <p>Our commitment to delivering high-standard results ensures that we consistently exceed client expectations.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Members section -->
    <section id="team" class="content-section">
        <div class="container">
            <div class="section-header text-center">
                <div class="team-label">OUR <span>EXPERT TEAM</span></div>
                <h2 class="section-title">Expert Team Members</h2>
            </div>

            <div class="team-grid">
                <div class="team-member">
                    <div class="member-image" style="background-color: #f5f5f5;">
                        <img src="images/karthik.jpg" alt="Karthikeya Singh">
                        <a href="https://www.linkedin.com/in/kartikeya-singh-028685363" target="_blank" class="linkedin-button">
                            <i class="fa-brands fa-linkedin"></i>
                        </a>
                    </div>
                    <div class="member-info" style="background-color: #ffffff;">
                        <h3 class="member-name">Karthikeya Singh</h3>
                        <p class="member-position" style="color: #333333; font-weight: 700;">Senior Design Engineer</p>
                    </div>
                </div>

                <div class="team-member">
                    <div class="member-image" style="background-color: #f5f5f5;">
                        <img src="images/vishnu.jpg" alt="Vishnu Menon">
                        <a href="https://www.linkedin.com/in/vishnu-menon-6130b7125" target="_blank" class="linkedin-button">
                            <i class="fa-brands fa-linkedin"></i>
                        </a>
                    </div>
                    <div class="member-info" style="background-color: #ffffff;">
                        <h3 class="member-name">Vishnu Menon</h3>
                        <p class="member-position" style="color: #333333; font-weight: 700;">Director</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact section start -->
    <section class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-form-wrapper">
                        <div class="section-tag">Get In Touch</div>
                        <h2 class="section-title">Ready to Switch to <span class="highlight-green">Clean Energy?</span></h2>
                        <p>Fill out the form below and our team will get back to you within 24 hours to discuss your renewable energy needs.</p>
                        <form class="contact-form" id="contactForm"
                              action="https://formspree.io/f/movddbga"
                              method="POST"
                              data-netlify="true"
                              name="contact">
                            <!-- Netlify form name (hidden) -->
                            <input type="hidden" name="form-name" value="contact">
                            <!-- Formspree redirect URL -->
                            <input type="hidden" name="_next" value="https://revolutionenergyindia.in/thank-you.html">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="Your Name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="email" name="email" id="email" class="form-control" placeholder="Your Email" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="tel" name="phone" id="phone" class="form-control" placeholder="Phone Number">
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <textarea name="message" id="message" class="form-control" rows="4" placeholder="Your Message" required></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </div>
                                <div class="col-12 mt-3">
                                    <div id="form-message" class="alert" style="display: none;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-info-wrapper">
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-phone"></i>
                            </div>
                            <div class="content">
                                <h5>Call Us</h5>
                                <p><a href="javascript:void(0)" class="copy-number" data-number="9633126288">9633126288</a></p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div class="content">
                                <h5>Email Us</h5>
                                <p class="email-container">
                                    <a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" title="<EMAIL>">
                                        <EMAIL>
                                    </a>
                                </p>
                                <p class="email-container">
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <div class="content">
                                <h5>Visit Us</h5>
                                <p class="address-line">No 27 New, MGR Main Rd, Kandhanchavadi,</p>
                                <p class="address-line">Perungudi, Chennai, Tamil Nadu 600096, India</p>
                            </div>
                        </div>
                        <div class="contact-map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3887.4657686057393!2d80.24863007486515!3d13.007999714280781!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5267a0673d1e8f%3A0x5b8f47d9b7e62f19!2sMGR%20Main%20Rd%2C%20Kandhanchavadi%2C%20Perungudi%2C%20Chennai%2C%20Tamil%20Nadu%20600096!5e0!3m2!1sen!2sin!4v1689321234567!5m2!1sen!2sin" width="100%" height="250" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact section end -->

    <!-- Footer start -->
    <footer class="footer">
        <div class="footer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-widget about-widget">
                            <img src="images/logo-02.png" alt="logo" class="footer-logo">
                            <p>REIN is dedicated to providing sustainable energy solutions that help reduce carbon footprints while saving costs. Our mission is to accelerate the transition to clean, renewable energy.</p>
                            <div class="social-links">
                                <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                                <a href="#"><i class="fa-brands fa-twitter"></i></a>
                                <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                                <a href="#"><i class="fa-brands fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Quick Links</h4>
                            <ul class="footer-links">
                                <li><a href="index.html">Home</a></li>
                                <li><a href="aboutus.html">About Us</a></li>
                                <li><a href="service.html">Services</a></li>
                                <li><a href="project.html">Projects</a></li>
                                <li><a href="contact.html">Contact</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Our Services</h4>
                            <ul class="footer-links">
                                <li><a href="#">Solar Installation</a></li>
                                <li><a href="#">Wind Energy Solutions</a></li>
                                <li><a href="#">Energy Audit & Monitoring</a></li>
                                <li><a href="#">Maintenance & Support</a></li>
                                <li><a href="#">Consultancy Services</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Newsletter</h4>
                            <p>Subscribe to our newsletter to receive updates on the latest renewable energy trends and our services.</p>
                            <form class="newsletter-form">
                                <input type="email" placeholder="Your Email Address">
                                <button type="submit"><i class="fa-solid fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; 2025 REIN. All Rights Reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>Designed with <i class="fa-solid fa-heart"></i> for a greener future</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer end -->

    <!-- pre loader area start -->
    <div id="elevate-load" style="display: none;">
        <div class="loader-wrapper">
            <div class="lds-ellipsis">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
    </div>
    <!-- pre loader area end -->

    <!-- search popup area start -->
    <div class="search-input-area">
        <div class="container">
            <div class="search-input-inner">
                <div class="input-div">
                    <input type="text" id="searchInput1" placeholder="Search Keywords">
                    <button><i class="far fa-search"></i></button>
                </div>
            </div>
        </div>
        <div id="searchResult1" class="search-result-area">
            <ul>
                <li><a href="#">solar</a></li>
                <li><a href="#">wind</a></li>
            </ul>
        </div>
    </div>
    <!-- search popup area end -->

    <!-- JS Files -->
    <script src="js/jquery-3.6.0.min.js"></script>
    <!-- Fallback for jQuery -->
    <script>
        if (typeof jQuery === 'undefined') {
            document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
        }
    </script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/swiper.min.js"></script>
    <script src="js/metismenu.js"></script>
    <script src="js/wow.min.js"></script>
    <!-- Load main.js with error handling -->
    <script>
        try {
            // Create a script element
            var mainScript = document.createElement('script');
            mainScript.src = 'js/main.js';
            mainScript.onerror = function() {
                console.error('Failed to load main.js');
                // Make sure the page is visible even if main.js fails
                document.getElementById('elevate-load').style.display = 'none';
                document.body.style.overflow = 'auto';
            };
            document.body.appendChild(mainScript);
        } catch(e) {
            console.error('Error loading main.js:', e);
            // Make sure the page is visible even if there's an error
            document.getElementById('elevate-load').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    </script>

    <!-- Custom scripts for this page -->
    <script>
        // Ensure the page loads even if there are issues with the preloader
        window.onload = function() {
            document.getElementById('elevate-load').style.display = 'none';
            document.body.style.overflow = 'auto';
        };

        // Fallback to make sure page loads after 3 seconds regardless of other scripts
        setTimeout(function() {
            document.getElementById('elevate-load').style.display = 'none';
            document.body.style.overflow = 'auto';
        }, 3000);

        $(document).ready(function() {
            // Hide preloader immediately when jQuery is ready
            $('#elevate-load').hide();
            $('body').css('overflow', 'auto');

            // Mobile Navbar Functionality - Simplified
            console.log('Mobile navbar initialized - Services dropdown removed');

            // Set active class for current page in mobile navbar
            const currentPath = window.location.pathname;
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            mobileNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    // Remove active class from all items
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    // Add active class to current item
                    item.classList.add('active');
                    console.log('Set active mobile nav item:', href);
                }
            });




            // Initialize metisMenu for mobile dropdown functionality
            if (typeof $.fn.metisMenu === 'function') {
                $('#mobile-menu-active').metisMenu();
            }

            // Solar Solutions Tabs
            $('.tab-btn').on('click', function() {
                var tabId = $(this).data('tab');

                // Remove active class from all buttons and content
                $('.tab-btn').removeClass('active');
                $('.tab-content').removeClass('active');

                // Add active class to current button and content
                $(this).addClass('active');
                $('#' + tabId + '-content').addClass('active');
            });

            // Add click event listeners to all elements with class 'copy-number'
            $('.copy-number').on('click', function(e) {
                e.preventDefault();

                // Get the phone number from the data attribute
                const phoneNumber = $(this).data('number');

                // Create a temporary input element
                const tempInput = document.createElement('input');
                tempInput.value = phoneNumber;
                document.body.appendChild(tempInput);

                // Select and copy the text
                tempInput.select();
                document.execCommand('copy');

                // Remove the temporary element
                document.body.removeChild(tempInput);

                // Show feedback to the user
                const originalText = $(this).text();
                $(this).text('Copied!');

                // Reset the text after a short delay
                setTimeout(() => {
                    $(this).text(originalText);
                }, 1500);
            });
        });
    </script>
</body>
</html>
