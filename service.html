<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.png">
    <title>Services - REIN</title>
    <meta name="description" content="REIN - Comprehensive renewable energy services including solar, wind, and energy management solutions.">

    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/fontawesome-6.css">
    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/swiper.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/metimenu.css">
    <link rel="stylesheet" href="css/animate.css">
    <!-- bootstrap css -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Custom css -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Custom color palette -->
    <link rel="stylesheet" href="css/custom-palette.css">
    <!-- New color scheme -->
    <link rel="stylesheet" href="css/new-color-scheme.css">
    <!-- Improved layout -->
    <link rel="stylesheet" href="css/improved-layout.css">
    <!-- Mobile responsive styles -->
    <link rel="stylesheet" href="css/mobile-responsive.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        .page-banner {
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/33.jpg') no-repeat center center;
            background-size: cover;
            padding: 100px 0;
            text-align: center;
            color: white;
        }

        .services-grid {
            padding: 100px 0;
            background-color: #f9f9f9;
        }

        /* Section header styling */
        .services-grid .section-tag {
            display: inline-block;
            background-color: rgba(74, 171, 61, 0.1);
            color: var(--primary-color);
            padding: 8px 20px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 15px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .services-grid .title {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .services-grid .highlight-green {
            color: var(--primary-color);
            position: relative;
        }

        .services-grid .section-description {
            font-size: 18px;
            max-width: 700px;
            margin: 0 auto 60px;
            color: #666;
            line-height: 1.7;
        }

        /* Service cards container */
        .services-container {
            margin-top: 30px;
        }

        /* Enhanced Let's Talk button styling */
        .contact-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(51, 96, 33, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .contact-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
            z-index: -1;
            transition: opacity 0.3s ease;
            opacity: 0;
        }

        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(51, 96, 33, 0.3);
            color: white;
        }

        .contact-btn:hover:before {
            opacity: 1;
        }

        .contact-btn .btn-text {
            margin-right: 10px;
        }

        .contact-btn .btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .contact-btn:hover .btn-icon {
            transform: translateX(4px);
        }

        /* Service card styling */
        .service-card {
            background-color: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.4s ease;
            height: 100%;
            margin-bottom: 50px;
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .service-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .service-image {
            height: 220px;
            overflow: hidden;
            position: relative;
        }

        .service-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s ease;
        }

        .service-image::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50px;
            background: linear-gradient(to top, rgba(255,255,255,0.8), transparent);
        }

        .service-card:hover .service-image img {
            transform: scale(1.1);
        }

        .service-content {
            padding: 40px 30px;
            position: relative;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: -40px;
            left: 30px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            border: 5px solid white;
        }

        .service-card:hover .service-icon {
            transform: rotateY(180deg);
            background-color: var(--secondary-color);
        }

        .service-icon i {
            font-size: 32px;
            color: white;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-icon i {
            transform: rotateY(180deg);
        }

        .service-title {
            margin-top: 30px;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-dark);
            position: relative;
            padding-bottom: 15px;
        }

        .service-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .service-card:hover .service-title::after {
            width: 100px;
            background-color: var(--secondary-color);
        }

        .service-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.7;
            font-size: 16px;
            min-height: 110px;
        }

        .service-link {
            display: inline-flex;
            align-items: center;
            color: var(--primary-color);
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 10px 0;
            font-size: 16px;
            border-top: 1px solid #eee;
            width: 100%;
            justify-content: space-between;
        }

        .service-link span {
            transition: all 0.3s ease;
        }

        .service-link i {
            margin-left: 8px;
            transition: transform 0.3s ease;
            background-color: rgba(74, 171, 61, 0.1);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .service-link:hover {
            color: var(--secondary-color);
        }

        .service-link:hover span {
            transform: translateX(5px);
        }

        .service-link:hover i {
            transform: translateX(5px);
            background-color: rgba(253, 143, 20, 0.1);
        }

        /* Responsive adjustments */
        @media (max-width: 991px) {
            .service-description {
                min-height: auto;
            }

            .services-grid .title {
                font-size: 36px;
            }
        }

        @media (max-width: 767px) {
            .services-grid {
                padding: 70px 0;
            }

            .services-grid .title {
                font-size: 30px;
            }

            .service-card {
                margin-bottom: 40px;
            }
        }

        /* Contact Info Section Styling */
        .contact-info-section {
            padding: 80px 0;
            background-color: #f9f9f9;
            position: relative;
        }

        .contact-card {
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .contact-info-content {
            padding: 50px;
        }

        .contact-info-content .section-title {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
            position: relative;
            padding-bottom: 15px;
        }

        .contact-info-content .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: var(--primary-color);
        }

        .contact-info-content .section-description {
            font-size: 18px;
            line-height: 1.7;
            color: #666;
            margin-bottom: 30px;
        }

        /* Contact Info Card Styling */
        .contact-info-card {
            background-color: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .contact-info-header {
            background-color: var(--primary-color);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .contact-info-header h5 {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .contact-info-body {
            padding: 20px;
        }

        .contact-info-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .contact-info-item:last-child {
            border-bottom: none;
        }

        .contact-info-item .icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(74, 171, 61, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .contact-info-item:hover .icon {
            background: var(--primary-color);
        }

        .contact-info-item .icon i {
            font-size: 18px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .contact-info-item:hover .icon i {
            color: white;
        }

        .contact-info-item a {
            font-size: 16px;
            color: #555;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            font-weight: 500;
        }

        .contact-info-item a:hover {
            color: var(--primary-color);
        }

        .contact-image {
            height: 100%;
            overflow: hidden;
            border-radius: 0 20px 20px 0;
        }

        .contact-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .contact-card:hover .contact-image img {
            transform: scale(1.05);
        }

        @media (max-width: 991px) {
            .contact-info-content {
                padding: 40px 30px;
            }

            .contact-info-content .section-title {
                font-size: 30px;
            }

            .contact-image {
                border-radius: 0 0 20px 20px;
                height: 300px;
            }
        }

        @media (max-width: 767px) {
            .contact-info-section {
                padding: 60px 0;
            }

            .contact-info-content {
                padding: 30px 20px;
            }

            .contact-info-content .section-title {
                font-size: 26px;
            }

            .contact-info-content .section-description {
                font-size: 16px;
            }

            .contact-info-card {
                margin-bottom: 20px;
            }

            .contact-info-header {
                padding: 12px 15px;
            }

            .contact-info-header h5 {
                font-size: 16px;
            }

            .contact-info-body {
                padding: 15px;
            }

            .contact-info-item {
                padding: 10px 0;
            }

            .contact-info-item .icon {
                width: 36px;
                height: 36px;
            }

            .contact-info-item .icon i {
                font-size: 16px;
            }

            .contact-info-item a {
                font-size: 14px;
                word-break: break-word;
            }

            .contact-image {
                height: 250px;
            }

            /* Mobile menu button improvements */
            .mobile-menu-trigger {
                padding: 12px;
                cursor: pointer;
                z-index: 100;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 8px;
                transition: all 0.3s ease;
                min-width: 48px;
                min-height: 48px;
                touch-action: manipulation;
            }

            .mobile-menu-trigger:hover {
                background-color: rgba(0, 0, 0, 0.4);
            }

            .mobile-menu-trigger:active {
                background-color: rgba(0, 0, 0, 0.6);
                transform: scale(0.95);
            }

            .mobile-menu-trigger svg {
                display: block;
                width: 24px;
                height: 24px;
            }

            /* Footer contact info responsive adjustments */
            .footer-contact-info {
                text-align: center;
                margin: 15px 0;
            }
        }

        /* Footer contact info styling */
        .footer-contact-info {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .footer-contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .footer-contact-item:hover {
            transform: translateY(-3px);
        }

        .footer-contact-item i {
            width: 32px;
            height: 32px;
            background-color: rgba(74, 171, 61, 0.1);
            color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .footer-contact-item:hover i {
            background-color: var(--primary-color);
            color: white;
        }

        .footer-contact-item a {
            color: #999;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .footer-contact-item a:hover {
            color: var(--primary-color);
        }

        .footer-contact-item .address-text {
            color: #999;
            font-size: 14px;
            line-height: 1.4;
        }

        .contact-info-item span {
            font-size: 15px;
            color: #555;
            line-height: 1.5;
        }

        /* Footer bottom enhanced styling */
        .footer-bottom {
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-bottom .copyright {
            margin-bottom: 0;
            font-size: 14px;
        }

        .footer-bottom-links {
            display: flex;
            gap: 20px;
            justify-content: flex-end;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .footer-bottom-links li a {
            font-size: 14px;
            color: #999;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-bottom-links li a:hover {
            color: var(--primary-color);
        }

        @media (max-width: 991px) {
            .footer-bottom-links {
                justify-content: center;
                margin-top: 15px;
            }

            .footer-bottom .copyright {
                text-align: center;
            }

            .footer-bottom .text-end {
                text-align: center !important;
            }
        }

        /* Enhanced Services Dropdown Menu Styling */
        .main-nav-one ul li.has-dropdown .submenu {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%) !important;
            border-radius: 15px !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
            border: 1px solid rgba(230, 140, 58, 0.1) !important;
            padding: 15px 0 !important;
            min-width: 280px !important;
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transform: translateY(-20px) !important;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
            z-index: 9999 !important;
        }

        .main-nav-one ul li.has-dropdown:hover .submenu {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li {
            margin: 0 !important;
            padding: 0 !important;
            border-bottom: none !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a {
            display: block !important;
            padding: 15px 25px !important;
            color: #2C3E50 !important;
            font-weight: 600 !important;
            font-size: 15px !important;
            text-decoration: none !important;
            border-bottom: 1px solid rgba(230, 140, 58, 0.1) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li:last-child a {
            border-bottom: none !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(230, 140, 58, 0.1) 0%, rgba(51, 96, 33, 0.1) 100%);
            transition: all 0.3s ease;
            z-index: 0;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover {
            color: #E68C3A !important;
            padding-left: 35px !important;
            background-color: rgba(230, 140, 58, 0.05) !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover::before {
            left: 0;
        }

        /* Add icons to dropdown items */
        .main-nav-one ul li.has-dropdown .submenu li a::after {
            content: '\f105';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #E68C3A;
            font-size: 12px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .main-nav-one ul li.has-dropdown .submenu li a:hover::after {
            opacity: 1;
            right: 15px;
        }

        /* Enhanced dropdown arrow */
        .main-nav-one ul li.has-dropdown a::before {
            content: "\f078" !important;
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            color: #fff !important;
            font-size: 12px !important;
            transition: all 0.3s ease !important;
        }

        .main-nav-one ul li.has-dropdown:hover a::before {
            content: "\f077" !important;
            color: #E68C3A !important;
        }

        /* Special styling for active dropdown item */
        .main-nav-one ul li.has-dropdown .submenu li a.active {
            background: linear-gradient(135deg, #E68C3A 0%, #FF7A00 100%) !important;
            color: #fff !important;
        }

        .main-nav-one ul li.has-dropdown .submenu li a.active::after {
            color: #fff !important;
            opacity: 1 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .main-nav-one ul li.has-dropdown .submenu {
                min-width: 250px !important;
            }
        }

        @media (max-width: 991px) {
            .main-nav-one ul li.has-dropdown .submenu {
                position: static !important;
                opacity: 1 !important;
                visibility: visible !important;
                transform: none !important;
                box-shadow: none !important;
                border: none !important;
                border-radius: 0 !important;
                background: transparent !important;
                padding: 0 !important;
                margin-left: 20px !important;
            }

            .main-nav-one ul li.has-dropdown .submenu li a {
                padding: 10px 15px !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
                color: #fff !important;
            }

            .main-nav-one ul li.has-dropdown .submenu li a:hover {
                background-color: rgba(255, 255, 255, 0.1) !important;
                color: #E68C3A !important;
                padding-left: 25px !important;
            }
        }
    </style>
</head>

<body>
    <!-- Header area start -->
    <header class="header-four header--sticky">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="header-left">
                        <a href="index.html" class="logo-area">
                            <img src="images/logo-02.png" alt="logo">
                        </a>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 d-none d-md-block">
                    <div class="nav-area">
                        <!-- navigation area start -->
                        <div class="header-nav main-nav-one">
                            <nav>
                                <ul>
                                    <li>
                                        <a class="nav-link" href="index.html">Home</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="aboutus.html">About</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="project.html">Project</a>
                                    </li>
                                    <li class="has-dropdown">
                                        <a class="nav-link active" href="service.html">Services</a>
                                        <ul class="submenu">
                                            <li><a href="design-and-consultancy.html">Design & Consultancy</a></li>
                                            <li><a href="solar-battery-ev.html">Solar, Battery & EV</a></li>
                                            <li><a href="wind.html">Wind</a></li>
                                            <li><a href="energy-audit-monitoring.html">Energy Audit, Monitoring & Management</a></li>
                                            <li><a href="solar-panel-servicing.html">Servicing</a></li>
                                        </ul>
                                    </li>
                                    <li><a class="nav-link" href="contact.html">Contact</a></li>
                                </ul>
                            </nav>
                        </div>
                        <!-- navigation area end -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-6">
                    <div class="header-right">
                        <div class="action-button-menu">
                            <a href="contact.html" class="contact-btn">
                                <span class="btn-text">Let's Talk</span>
                                <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                            </a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header area end -->

    <!-- New Mobile Navigation Bar -->
    <nav class="mobile-navbar d-md-none">
        <div class="mobile-nav-container">
            <a href="index.html" class="mobile-nav-item">
                <i class="fa-solid fa-home"></i>
                <span>Home</span>
            </a>
            <a href="aboutus.html" class="mobile-nav-item">
                <i class="fa-solid fa-info-circle"></i>
                <span>About</span>
            </a>
            <a href="project.html" class="mobile-nav-item">
                <i class="fa-solid fa-project-diagram"></i>
                <span>Projects</span>
            </a>
            <a href="service.html" class="mobile-nav-item active">
                <i class="fa-solid fa-solar-panel"></i>
                <span>Services</span>
            </a>
            <a href="contact.html" class="mobile-nav-item">
                <i class="fa-solid fa-envelope"></i>
                <span>Contact</span>
            </a>
        </div>
    </nav>

    <!-- Legacy elements kept for compatibility -->
    <div id="side-bar" class="side-bar d-none"></div>
    <div id="anywhere-home" class="d-none"></div>

    <!-- Page Banner -->
    <section class="page-banner">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h1>Our Services</h1>
                    <p class="lead">Comprehensive renewable energy solutions for a sustainable future</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Grid -->
    <section class="services-grid">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <div class="section-tag wow fadeInUp" data-wow-delay="0.1s">What We Offer</div>
                    <h2 class="title wow fadeInUp" data-wow-delay="0.2s">Our Comprehensive <span class="highlight-green">Energy Solutions</span></h2>
                    <p class="section-description wow fadeInUp" data-wow-delay="0.3s">Explore our range of specialized services designed to meet your renewable energy needs</p>
                </div>
            </div>

            <div class="services-container">
                <div class="row">
                    <!-- Design & Consultancy -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.1s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="images/33_2.jpg" alt="Design & Consultancy">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-compass-drafting"></i>
                                </div>
                                <h3 class="service-title">Design & Consultancy</h3>
                                <p class="service-description">Expert guidance and custom design solutions for renewable energy projects, tailored to your specific needs and site conditions.</p>
                                <a href="design-and-consultancy.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Solar, Battery & EV -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.2s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="images/33.jpg" alt="Solar, Battery & EV">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-solar-panel"></i>
                                </div>
                                <h3 class="service-title">Solar, Battery & EV</h3>
                                <p class="service-description">Comprehensive solar panel installation, battery storage solutions, and electric vehicle charging infrastructure for homes and businesses.</p>
                                <a href="solar-battery-ev.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Wind -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.3s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="images/34_1.jpg" alt="Wind Energy">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-wind"></i>
                                </div>
                                <h3 class="service-title">Wind Energy</h3>
                                <p class="service-description">Wind turbine installation and maintenance services for residential, commercial, and industrial applications, harnessing the power of wind.</p>
                                <a href="wind.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Energy Audit, Monitoring & Management -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.4s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="imagesprj/img.jpg" alt="Energy Audit & Monitoring">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-chart-line"></i>
                                </div>
                                <h3 class="service-title">Energy Audit & Monitoring</h3>
                                <p class="service-description">Comprehensive energy assessment and real-time monitoring systems to optimize your energy consumption and maximize efficiency.</p>
                                <a href="energy-audit-monitoring.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Servicing -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.5s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="images/15.jpg" alt="Servicing">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-screwdriver-wrench"></i>
                                </div>
                                <h3 class="service-title">Servicing</h3>
                                <p class="service-description">Regular maintenance, repair, and optimization services for all your renewable energy systems to ensure peak performance and longevity.</p>
                                <a href="solar-panel-servicing.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Energy Management -->
                    <div class="col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="0.6s">
                        <div class="service-card">
                            <div class="service-image">
                                <img src="images/34.jpg" alt="Energy Management">
                            </div>
                            <div class="service-content">
                                <div class="service-icon">
                                    <i class="fa-solid fa-bolt"></i>
                                </div>
                                <h3 class="service-title">Energy Management</h3>
                                <p class="service-description">Smart energy management solutions that help you control and optimize your energy usage, reducing costs and environmental impact.</p>
                                <a href="energy-audit-monitoring.html" class="service-link">
                                    <span>Learn More</span>
                                    <i class="fa-solid fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact section start -->
    <section class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-form-wrapper">
                        <div class="section-tag">Get In Touch</div>
                        <h2 class="section-title">Ready to Switch to <span class="highlight-green">Clean Energy?</span></h2>
                        <p>Fill out the form below and our team will get back to you within 24 hours to discuss your renewable energy needs.</p>
                        <form class="contact-form" id="contactForm"
                              action="https://formspree.io/f/movddbga"
                              method="POST"
                              data-netlify="true"
                              name="contact">
                            <!-- Netlify form name (hidden) -->
                            <input type="hidden" name="form-name" value="contact">
                            <!-- Formspree redirect URL -->
                            <input type="hidden" name="_next" value="https://revolutionenergyindia.in/thank-you.html">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="Your Name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="email" name="email" id="email" class="form-control" placeholder="Your Email" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="tel" name="phone" id="phone" class="form-control" placeholder="Phone Number">
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <textarea name="message" id="message" class="form-control" rows="4" placeholder="Your Message" required></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </div>
                                <div class="col-12 mt-3">
                                    <div id="form-message" class="alert" style="display: none;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-info-wrapper">
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-phone"></i>
                            </div>
                            <div class="content">
                                <h5>Call Us</h5>
                                <p><a href="javascript:void(0)" class="copy-number" data-number="9633126288">9633126288</a></p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div class="content">
                                <h5>Email Us</h5>
                                <p class="email-container">
                                    <a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" title="<EMAIL>">
                                        <EMAIL>
                                    </a>
                                </p>
                                <p class="email-container">
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <div class="content">
                                <h5>Visit Us</h5>
                                <p class="address-line">No 27 New, MGR Main Rd, Kandhanchavadi,</p>
                                <p class="address-line">Perungudi, Chennai, Tamil Nadu 600096, India</p>
                            </div>
                        </div>
                        <div class="contact-map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3887.4657686057393!2d80.24863007486515!3d13.007999714280781!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5267a0673d1e8f%3A0x5b8f47d9b7e62f19!2sMGR%20Main%20Rd%2C%20Kandhanchavadi%2C%20Perungudi%2C%20Chennai%2C%20Tamil%20Nadu%20600096!5e0!3m2!1sen!2sin!4v1689321234567!5m2!1sen!2sin" width="100%" height="250" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact section end -->

    <!-- Footer start -->
    <footer class="footer">
        <div class="footer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-widget about-widget">
                            <img src="images/logo-02.png" alt="logo" class="footer-logo">
                            <p>REIN is dedicated to providing sustainable energy solutions that help reduce carbon footprints while saving costs. Our mission is to accelerate the transition to clean, renewable energy.</p>

                            <div class="social-links">
                                <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                                <a href="#"><i class="fa-brands fa-twitter"></i></a>
                                <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                                <a href="#"><i class="fa-brands fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Quick Links</h4>
                            <ul class="footer-links">
                                <li><a href="index.html">Home</a></li>
                                <li><a href="aboutus.html">About Us</a></li>
                                <li><a href="service.html">Services</a></li>
                                <li><a href="project.html">Projects</a></li>
                                <li><a href="contact.html">Contact</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Our Services</h4>
                            <ul class="footer-links">
                                <li><a href="design-and-consultancy.html">Design & Consultancy</a></li>
                                <li><a href="solar-battery-ev.html">Solar, Battery & EV</a></li>
                                <li><a href="wind.html">Wind Energy</a></li>
                                <li><a href="energy-audit-monitoring.html">Energy Audit & Monitoring</a></li>
                                <li><a href="solar-panel-servicing.html">Servicing</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Newsletter</h4>
                            <p>Subscribe to our newsletter to receive updates on the latest renewable energy trends and our services.</p>
                            <form class="newsletter-form">
                                <input type="email" placeholder="Your Email Address">
                                <button type="submit"><i class="fa-solid fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-4 col-md-6">
                        <p class="copyright">© 2025 REIN. All Rights Reserved.</p>
                    </div>
                    <div class="col-lg-4 col-md-6 footer-contact-info">
                        <div class="footer-contact-item">
                            <i class="fa-solid fa-phone"></i>
                            <a href="javascript:void(0)" class="copy-number" data-number="9633126288">9633126288</a>
                        </div>
                        <div class="footer-contact-item">
                            <i class="fa-solid fa-envelope"></i>
                            <a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank"><EMAIL></a>
                        </div>
                        <div class="footer-contact-item">
                            <i class="fa-solid fa-location-dot"></i>
                            <span class="address-text">No 27 New, MGR Main Rd, Chennai</span>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-12 text-end">
                        <ul class="footer-bottom-links">
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                            <li><a href="#">Cookie Policy</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer end -->

    <!-- JavaScript files -->
    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/swiper-bundle.min.js"></script>
    <script src="js/metismenu.min.js"></script>
    <script src="js/wow.min.js"></script>
    <script src="js/main.js"></script>

    <!-- Initialize WOW.js for animations -->
    <script>
        new WOW().init();
    </script>

    <!-- Custom JS for copying phone numbers and mobile menu -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to all elements with class 'copy-number'
            const phoneNumbers = document.querySelectorAll('.copy-number');

            phoneNumbers.forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get the phone number from the data attribute
                    const phoneNumber = this.getAttribute('data-number');

                    // Create a temporary input element
                    const tempInput = document.createElement('input');
                    tempInput.value = phoneNumber;
                    document.body.appendChild(tempInput);

                    // Select and copy the text
                    tempInput.select();
                    document.execCommand('copy');

                    // Remove the temporary element
                    document.body.removeChild(tempInput);

                    // Show feedback to the user
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';

                    // Reset the text after a short delay
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1500);
                });
            });

            // New Mobile Navbar Functionality - Fixed Dropdown
            const mobileDropdown = document.querySelector('.mobile-dropdown');
            const mobileDropdownToggle = document.querySelector('.mobile-dropdown-toggle');

            if (mobileDropdownToggle && mobileDropdown) {
                // Improved click handler for the dropdown toggle
                mobileDropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle the active class
                    mobileDropdown.classList.toggle('active');

                    // Force display block on the dropdown menu when active
                    const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                    if (dropdownMenu) {
                        if (mobileDropdown.classList.contains('active')) {
                            dropdownMenu.style.display = 'block';
                        } else {
                            // Use setTimeout to allow for animation
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (mobileDropdown && !mobileDropdown.contains(e.target)) {
                        mobileDropdown.classList.remove('active');

                        // Hide the dropdown menu
                        const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                        if (dropdownMenu) {
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });

                // Close dropdown when scrolling
                window.addEventListener('scroll', function() {
                    if (mobileDropdown) {
                        mobileDropdown.classList.remove('active');

                        // Hide the dropdown menu
                        const dropdownMenu = mobileDropdown.querySelector('.mobile-dropdown-menu');
                        if (dropdownMenu) {
                            setTimeout(() => {
                                if (!mobileDropdown.classList.contains('active')) {
                                    dropdownMenu.style.display = 'none';
                                }
                            }, 300);
                        }
                    }
                });
            }

            // Set active class for current page in mobile navbar
            const currentPath = window.location.pathname;
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            mobileNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    // Remove active class from all items
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    // Add active class to current item
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
